const fs = require('fs');

// Read the file
let content = fs.readFileSync('src/components/LanguageContext.tsx', 'utf8');

console.log('Starting translation key normalization...');

// Define the mapping of keys that should be consistent across all languages
const keyMappings = {
  // Basic navigation
  'dashboard-alt': 'dashboard',
  'dashboard-alt2': 'dashboard', 
  'dashboard-alt3': 'dashboard',
  'dashboard-alt4': 'dashboard',
  'dashboard-alt5': 'dashboard',
  'market-alt': 'market',
  'market-alt2': 'market',
  'market-alt3': 'market', 
  'market-alt4': 'market',
  'market-alt5': 'market',
  'export-alt': 'export',
  'export-alt2': 'export',
  'export-alt3': 'export',
  'export-alt4': 'export',
  'export-alt5': 'export',
  
  // Titles and descriptions
  'loans-title-alt': 'loans-title',
  'loans-title-alt2': 'loans-title',
  'loans-title-alt3': 'loans-title',
  'loans-title-alt4': 'loans-title',
  'loans-title-alt5': 'loans-title',
  'loans-description-alt': 'loans-description',
  'loans-description-alt2': 'loans-description',
  'loans-description-alt3': 'loans-description',
  'loans-description-alt4': 'loans-description',
  'loans-description-alt5': 'loans-description',
  'market-title-alt': 'market-title',
  'market-title-alt2': 'market-title',
  'market-title-alt3': 'market-title',
  'market-title-alt4': 'market-title',
  'market-title-alt5': 'market-title',
  'market-description-alt': 'market-description',
  'market-description-alt2': 'market-description',
  'market-description-alt3': 'market-description',
  'market-description-alt4': 'market-description',
  'market-description-alt5': 'market-description',
  'machinery-title-alt': 'machinery-title',
  'machinery-title-alt2': 'machinery-title',
  'machinery-title-alt3': 'machinery-title',
  'machinery-title-alt4': 'machinery-title',
  'machinery-title-alt5': 'machinery-title',
  'machinery-title-alt6': 'machinery-title',
  'machinery-description-alt': 'machinery-description',
  'machinery-description-alt2': 'machinery-description',
  'machinery-description-alt3': 'machinery-description',
  'machinery-description-alt4': 'machinery-description',
  'machinery-description-alt5': 'machinery-description',
  'machinery-description-alt6': 'machinery-description',
  'export-description-alt': 'export-description',
  'export-description-alt2': 'export-description',
  'export-description-alt3': 'export-description',
  'export-description-alt4': 'export-description',
  'export-description-alt5': 'export-description',
  'export-description-alt6': 'export-description',
  'monitoring-description-alt': 'monitoring-description',
  'monitoring-description-alt2': 'monitoring-description',
  'monitoring-description-alt3': 'monitoring-description',
  'monitoring-description-alt4': 'monitoring-description',
  'monitoring-description-alt5': 'monitoring-description',
  'monitoring-description-alt6': 'monitoring-description',
  'contact-description-alt': 'contact-description',
  'contact-description-alt2': 'contact-description',
  'contact-description-alt3': 'contact-description',
  'contact-description-alt4': 'contact-description',
  'contact-description-alt5': 'contact-description',
  'contact-description-alt6': 'contact-description',
  'dashboard-title-alt': 'dashboard-title',
  'dashboard-title-alt2': 'dashboard-title',
  'dashboard-title-alt3': 'dashboard-title',
  'dashboard-title-alt4': 'dashboard-title',
  'dashboard-title-alt5': 'dashboard-title',
  'dashboard-welcome-alt': 'dashboard-welcome',
  'dashboard-welcome-alt2': 'dashboard-welcome',
  'dashboard-welcome-alt3': 'dashboard-welcome',
  'dashboard-welcome-alt4': 'dashboard-welcome',
  'dashboard-welcome-alt5': 'dashboard-welcome',
  'crop-allocation-title-alt': 'crop-allocation-title',
  'crop-allocation-title-alt2': 'crop-allocation-title',
  'crop-allocation-title-alt3': 'crop-allocation-title',
  'crop-allocation-title-alt4': 'crop-allocation-title',
  'crop-allocation-title-alt5': 'crop-allocation-title',
  'crop-allocation-description-alt': 'crop-allocation-description',
  'crop-allocation-description-alt2': 'crop-allocation-description',
  'crop-allocation-description-alt3': 'crop-allocation-description',
  'crop-allocation-description-alt4': 'crop-allocation-description',
  'crop-allocation-description-alt5': 'crop-allocation-description',
  'plan-crops-alt': 'plan-crops',
  'plan-crops-alt2': 'plan-crops',
  'plan-crops-alt3': 'plan-crops',
  'plan-crops-alt4': 'plan-crops',
  'plan-crops-alt5': 'plan-crops',
  'footer-text-alt': 'footer-text',
  'footer-text-alt2': 'footer-text',
  'footer-text-alt3': 'footer-text',
  'footer-text-alt4': 'footer-text',
  'footer-text-alt5': 'footer-text',
  
  // Common UI elements
  'view-all-alt': 'view-all',
  'view-all-alt2': 'view-all',
  'view-all-alt3': 'view-all',
  'view-all-alt4': 'view-all',
  'view-all-alt5': 'view-all',
  'sort-by-alt': 'sort-by',
  'sort-by-alt2': 'sort-by',
  'sort-by-alt3': 'sort-by',
  'sort-by-alt4': 'sort-by',
  'sort-by-alt5': 'sort-by',
  'reviews-alt': 'reviews',
  'reviews-alt2': 'reviews',
  'reviews-alt3': 'reviews',
  'reviews-alt4': 'reviews',
  'reviews-alt5': 'reviews',
  'location-alt': 'location',
  'location-alt2': 'location',
  'location-alt3': 'location',
  'location-alt4': 'location',
  'location-alt5': 'location',
  'next-alt': 'next',
  'next-alt2': 'next',
  'next-alt3': 'next',
  'next-alt4': 'next',
  'next-alt5': 'next',
  'next-alt6': 'next',
  'next-alt7': 'next',
  'previous-alt': 'previous',
  'previous-alt2': 'previous',
  'previous-alt3': 'previous',
  'previous-alt4': 'previous',
  'previous-alt5': 'previous',
  'previous-alt6': 'previous',
  'previous-alt7': 'previous',
  'share-alt': 'share',
  'share-alt2': 'share',
  'share-alt3': 'share',
  'share-alt4': 'share',
  'share-alt5': 'share',
  'rating-alt': 'rating',
  'rating-alt2': 'rating',
  'rating-alt3': 'rating',
  'rating-alt4': 'rating',
  'rating-alt5': 'rating',

  // Labor and job related keys
  'labor-management-alt': 'labor-management',
  'labor-management-alt2': 'labor-management',
  'labor-management-alt3': 'labor-management',
  'labor-management-alt4': 'labor-management',
  'labor-management-alt5': 'labor-management',
  'labour-description-alt': 'labour-description',
  'labour-description-alt2': 'labour-description',
  'labour-description-alt3': 'labour-description',
  'labour-description-alt4': 'labour-description',
  'labour-description-alt5': 'labour-description',
  'labour-description-alt6': 'labour-description',
  'post-jobs-alt': 'post-jobs',
  'post-jobs-alt2': 'post-jobs',
  'post-jobs-alt3': 'post-jobs',
  'post-jobs-alt4': 'post-jobs',
  'post-jobs-alt5': 'post-jobs',
  'post-jobs-alt6': 'post-jobs',
  'specialization-alt': 'specialization',
  'specialization-alt2': 'specialization',
  'specialization-alt3': 'specialization',
  'specialization-alt4': 'specialization',
  'specialization-alt5': 'specialization',
  'specialization-alt6': 'specialization',
  'job-title-alt': 'job-title',
  'job-title-alt2': 'job-title',
  'job-title-alt3': 'job-title',
  'job-title-alt4': 'job-title',
  'job-title-alt5': 'job-title',
  'job-type-alt': 'job-type',
  'job-type-alt2': 'job-type',
  'job-type-alt3': 'job-type',
  'job-type-alt4': 'job-type',
  'job-type-alt5': 'job-type',
  'pay-rate-alt': 'pay-rate',
  'pay-rate-alt2': 'pay-rate',
  'pay-rate-alt3': 'pay-rate',
  'pay-rate-alt4': 'pay-rate',
  'pay-rate-alt5': 'pay-rate',
  'job-description-alt': 'job-description',
  'job-description-alt2': 'job-description',
  'job-description-alt3': 'job-description',
  'job-description-alt4': 'job-description',
  'job-description-alt5': 'job-description',
  'post-job-alt': 'post-job',
  'post-job-alt2': 'post-job',
  'post-job-alt3': 'post-job',
  'post-job-alt4': 'post-job',
  'post-job-alt5': 'post-job',
  'worker-hired-alt': 'worker-hired',
  'worker-hired-alt2': 'worker-hired',
  'worker-hired-alt3': 'worker-hired',
  'worker-hired-alt4': 'worker-hired',
  'worker-hired-alt5': 'worker-hired',
  'team-hired-alt': 'team-hired',
  'team-hired-alt2': 'team-hired',
  'team-hired-alt3': 'team-hired',
  'team-hired-alt4': 'team-hired',
  'team-hired-alt5': 'team-hired',
  'message-sent-alt': 'message-sent',
  'message-sent-alt2': 'message-sent',
  'message-sent-alt3': 'message-sent',
  'message-sent-alt4': 'message-sent',
  'message-sent-alt5': 'message-sent',
  'success-rate-alt': 'success-rate',
  'success-rate-alt2': 'success-rate',
  'success-rate-alt3': 'success-rate',
  'success-rate-alt4': 'success-rate',
  'success-rate-alt5': 'success-rate',

  // Subsidies
  'subsidies-available-alt': 'subsidies-available',
  'subsidies-available-alt2': 'subsidies-available',
  'subsidies-available-alt3': 'subsidies-available',
  'subsidies-available-alt4': 'subsidies-available',
  'subsidies-available-alt5': 'subsidies-available'
};

// Apply the key mappings
Object.entries(keyMappings).forEach(([oldKey, newKey]) => {
  const regex = new RegExp(`"${oldKey.replace(/[-/\\^$*+?.()|[\]{}]/g, '\\$&')}"\\s*:`, 'g');
  const replacement = `"${newKey}":`;
  content = content.replace(regex, replacement);
  console.log(`Replaced "${oldKey}" with "${newKey}"`);
});

console.log('Key normalization complete. Writing file...');

// Write the fixed content back
fs.writeFileSync('src/components/LanguageContext.tsx', content);
console.log('Translation keys normalized successfully!');
