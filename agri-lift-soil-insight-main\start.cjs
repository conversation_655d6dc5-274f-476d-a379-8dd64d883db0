#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting Agri-Lift Development Servers...\n');

// Check if directories exist
if (!fs.existsSync('backend')) {
  console.error('❌ Backend directory not found!');
  process.exit(1);
}

if (!fs.existsSync('package.json')) {
  console.error('❌ Root package.json not found!');
  process.exit(1);
}

// Function to spawn process
function spawnProcess(command, args, options = {}) {
  console.log(`Starting: ${command} ${args.join(' ')}`);
  
  const child = spawn(command, args, {
    stdio: 'inherit',
    shell: true,
    ...options
  });

  child.on('error', (error) => {
    console.error(`❌ Error starting ${command}:`, error.message);
  });

  return child;
}

// Start backend
console.log('📡 Starting Backend Server...');
const backend = spawnProcess('npm', ['run', 'dev'], {
  cwd: path.join(__dirname, 'backend')
});

// Wait a bit then start frontend
setTimeout(() => {
  console.log('🌐 Starting Frontend Development Server...');
  const frontend = spawnProcess('npm', ['run', 'dev:frontend-only']);
  
  // Handle shutdown
  process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down servers...');
    backend.kill('SIGINT');
    frontend.kill('SIGINT');
    process.exit(0);
  });
}, 3000);

console.log('\n✅ Servers are starting up!');
console.log('📡 Backend: http://localhost:5001');
console.log('🌐 Frontend: http://localhost:3000');
console.log('\n💡 Press Ctrl+C to stop both servers\n');

