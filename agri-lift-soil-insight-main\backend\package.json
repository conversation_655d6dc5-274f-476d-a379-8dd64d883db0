{"name": "agri-lift-backend", "version": "1.0.0", "description": "Backend for Agri-Lift Soil Insight application", "main": "server.js", "type": "commonjs", "scripts": {"start": "node server.js", "dev": "nodemon server.js --ignore uploads/", "test": "jest"}, "engines": {"node": ">=16.0.0"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "mongoose": "^8.0.3", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5"}, "devDependencies": {"nodemon": "^3.0.2"}}