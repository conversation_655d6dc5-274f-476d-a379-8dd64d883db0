{"name": "agri-lift-soil-insight", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:smart": "node start.cjs", "dev:frontend": "vite --port 3000 --host", "dev:backend": "cd backend && npm run dev", "dev:frontend-only": "vite --port 3000 --host", "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"", "start:frontend": "vite preview --port 3000", "start:backend": "cd backend && npm start", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview --port 3000", "install:all": "npm install && cd backend && npm install && cd ..", "clean": "powershell -Command \"Remove-Item -Recurse -Force node_modules,backend/node_modules,dist -ErrorAction SilentlyContinue\""}}