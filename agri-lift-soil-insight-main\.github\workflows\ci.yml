name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18.x'
  MONGODB_URI: mongodb://localhost:27017/agri-lift-test

jobs:
  # Frontend Tests
  frontend-test:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install frontend dependencies
      run: npm ci

    - name: Run frontend linting
      run: npm run lint

    - name: Run frontend type checking
      run: npm run type-check

    - name: Run frontend tests
      run: npm run test:coverage

    - name: Upload frontend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: frontend
        name: frontend-coverage

    - name: Build frontend
      run: npm run build

    - name: Upload frontend build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: frontend-build
        path: dist/

  # Backend Tests
  backend-test:
    runs-on: ubuntu-latest
    
    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017
        options: >-
          --health-cmd "mongosh --eval 'db.adminCommand(\"ping\")'"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: backend/package-lock.json

    - name: Install backend dependencies
      working-directory: ./backend
      run: npm ci

    - name: Run backend linting
      working-directory: ./backend
      run: npm run lint

    - name: Run backend tests
      working-directory: ./backend
      run: npm run test:coverage
      env:
        MONGODB_URI: ${{ env.MONGODB_URI }}
        JWT_SECRET: test-secret
        RAZORPAY_KEY_ID: test-key
        RAZORPAY_KEY_SECRET: test-secret

    - name: Upload backend coverage to Codecov
      uses: codecov/codecov-action@v3
      with:
        file: ./backend/coverage/lcov.info
        flags: backend
        name: backend-coverage

  # Security Scanning
  security-scan:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v2
      with:
        sarif_file: 'trivy-results.sarif'

    - name: Run npm audit for frontend
      run: npm audit --audit-level moderate

    - name: Run npm audit for backend
      working-directory: ./backend
      run: npm audit --audit-level moderate

  # End-to-End Tests
  e2e-test:
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test]
    
    services:
      mongodb:
        image: mongo:6.0
        ports:
          - 27017:27017

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: |
        npm ci
        cd backend && npm ci

    - name: Start backend server
      working-directory: ./backend
      run: |
        npm start &
        sleep 10
      env:
        MONGODB_URI: ${{ env.MONGODB_URI }}
        PORT: 5001

    - name: Build and start frontend
      run: |
        npm run build
        npm run preview &
        sleep 10

    - name: Install Playwright
      run: npx playwright install --with-deps

    - name: Run E2E tests
      run: npm run test:e2e

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-test-results
        path: test-results/

  # Build and Deploy (Production)
  deploy-production:
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test, security-scan, e2e-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    environment:
      name: production
      url: https://agri-lift-dairy.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build frontend for production
      run: npm run build
      env:
        VITE_API_URL: ${{ secrets.PROD_API_URL }}
        VITE_RAZORPAY_KEY: ${{ secrets.PROD_RAZORPAY_KEY }}

    - name: Deploy to AWS S3
      uses: aws-actions/configure-aws-credentials@v2
      with:
        aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
        aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
        aws-region: ap-south-1

    - name: Sync frontend to S3
      run: |
        aws s3 sync dist/ s3://${{ secrets.S3_BUCKET_NAME }} --delete
        aws cloudfront create-invalidation --distribution-id ${{ secrets.CLOUDFRONT_DISTRIBUTION_ID }} --paths "/*"

    - name: Deploy backend to AWS ECS
      run: |
        # Build and push Docker image
        docker build -t agri-lift-backend ./backend
        docker tag agri-lift-backend:latest ${{ secrets.ECR_REGISTRY }}/agri-lift-backend:latest
        aws ecr get-login-password --region ap-south-1 | docker login --username AWS --password-stdin ${{ secrets.ECR_REGISTRY }}
        docker push ${{ secrets.ECR_REGISTRY }}/agri-lift-backend:latest
        
        # Update ECS service
        aws ecs update-service --cluster agri-lift-cluster --service agri-lift-backend-service --force-new-deployment

    - name: Run database migrations
      run: |
        # Run any pending database migrations
        cd backend && npm run migrate
      env:
        MONGODB_URI: ${{ secrets.PROD_MONGODB_URI }}

    - name: Notify deployment success
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#deployments'
        text: '🚀 Production deployment successful!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Deploy Staging
  deploy-staging:
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test, security-scan]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    
    environment:
      name: staging
      url: https://staging.agri-lift-dairy.com

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build frontend for staging
      run: npm run build
      env:
        VITE_API_URL: ${{ secrets.STAGING_API_URL }}
        VITE_RAZORPAY_KEY: ${{ secrets.STAGING_RAZORPAY_KEY }}

    - name: Deploy to staging environment
      run: |
        # Deploy to staging infrastructure
        echo "Deploying to staging..."
        # Add staging deployment commands here

    - name: Run staging tests
      run: |
        # Run additional staging-specific tests
        npm run test:staging

    - name: Notify staging deployment
      uses: 8398a7/action-slack@v3
      with:
        status: success
        channel: '#deployments'
        text: '🧪 Staging deployment successful!'
      env:
        SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}

  # Performance Testing
  performance-test:
    runs-on: ubuntu-latest
    needs: [deploy-staging]
    if: github.ref == 'refs/heads/develop'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Lighthouse CI
      uses: treosh/lighthouse-ci-action@v9
      with:
        urls: |
          https://staging.agri-lift-dairy.com
          https://staging.agri-lift-dairy.com/dairy-lift/sell-produce
        configPath: './lighthouserc.json'
        uploadArtifacts: true
        temporaryPublicStorage: true

    - name: Run load testing with Artillery
      run: |
        npm install -g artillery
        artillery run performance-tests/load-test.yml

    - name: Upload performance results
      uses: actions/upload-artifact@v3
      with:
        name: performance-results
        path: performance-results/
